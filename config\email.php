<?php
/**
 * Email Configuration and Functions
 *
 * This file contains email-related functions and uses
 * configuration from config.php
 */

// Load configuration
require_once __DIR__ . "/config.php";

function sendPasswordResetEmail($to, $username, $resetLink, $operatorType = '') {
    $subject = "Password Reset Request - " . EMAIL_FROM_NAME;
    $message = getPasswordResetEmailTemplate($username, $resetLink, $operatorType);

    // Try SMTP first, fallback to mail() if SMTP fails
    $result = sendSMTPEmail($to, $subject, $message);
    if (!$result) {
        error_log("<PERSON><PERSON> failed, trying fallback mail() function");
        $result = sendFallbackEmail($to, $subject, $message);
    }
    return $result;
}

function sendPasswordResetConfirmationEmail($to, $username) {
    $subject = "Password Reset Successful - " . EMAIL_FROM_NAME;
    $message = getPasswordResetConfirmationTemplate($username);

    // Try SMTP first, fallback to mail() if SMTP fails
    $result = sendSMTPEmail($to, $subject, $message);
    if (!$result) {
        error_log("<PERSON><PERSON> failed, trying fallback mail() function");
        $result = sendFallbackEmail($to, $subject, $message);
    }
    return $result;
}

function getEmailHeaders() {
    $headers = "MIME-Version: 1.0\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8\r\n";
    $headers .= "From: " . EMAIL_FROM_NAME . " <" . EMAIL_FROM_ADDRESS . ">\r\n";
    $headers .= "Reply-To: " . EMAIL_FROM_ADDRESS . "\r\n";
    return $headers;
}

function getPasswordResetEmailTemplate($username, $resetLink, $operatorType = '') {
    $operatorTypeText = $operatorType ? " (" . htmlspecialchars($operatorType) . ")" : "";
    return "
    <html>
    <body style=\"font-family: Arial, sans-serif; padding: 20px;\">
        <h2 style=\"color: #3b82f6;\">Password Reset Request</h2>
        <p>Hello <strong>" . htmlspecialchars($username) . "</strong>" . $operatorTypeText . ",</p>
        <p>Click the link below to reset your password:</p>
        <p><a href=\"" . $resetLink . "\" style=\"background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;\">Reset Password</a></p>
        <p>This link expires in 1 hour.</p>
        <p>If you did not request this, please ignore this email.</p>
    </body>
    </html>";
}

function getPasswordResetConfirmationTemplate($username) {
    return "
    <html>
    <body style=\"font-family: Arial, sans-serif; padding: 20px;\">
        <h2 style=\"color: #10b981;\">Password Reset Successful</h2>
        <p>Hello <strong>" . htmlspecialchars($username) . "</strong>,</p>
        <p>Your password has been successfully reset.</p>
        <p>You can now log in with your new password.</p>
    </body>
    </html>";
}

function sendSMTPEmail($to, $subject, $message) {
    try {
        // Determine connection type based on port and encryption
        $context = stream_context_create();
        if (SMTP_ENCRYPTION === 'ssl' && SMTP_PORT == 465) {
            // SSL connection for port 465
            $smtp = fsockopen('ssl://' . SMTP_HOST, SMTP_PORT, $errno, $errstr, 30);
        } else {
            // Regular connection for other ports
            $smtp = fsockopen(SMTP_HOST, SMTP_PORT, $errno, $errstr, 30);
        }

        if (!$smtp) {
            error_log("SMTP Connection failed: $errstr ($errno)");
            return false;
        }

        // Read server response
        $response = fgets($smtp, 515);
        if (substr($response, 0, 3) != '220') {
            error_log("SMTP Server not ready: $response");
            fclose($smtp);
            return false;
        }

        // Send EHLO command
        fputs($smtp, "EHLO " . $_SERVER['HTTP_HOST'] . "\r\n");
        $response = fgets($smtp, 515);

        // Start TLS if using TLS (not SSL on port 465)
        if (SMTP_ENCRYPTION === 'tls' && SMTP_PORT != 465) {
            fputs($smtp, "STARTTLS\r\n");
            $response = fgets($smtp, 515);
            if (substr($response, 0, 3) != '220') {
                error_log("STARTTLS failed: $response");
                fclose($smtp);
                return false;
            }

            // Enable crypto
            if (!stream_socket_enable_crypto($smtp, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                error_log("Failed to enable TLS encryption");
                fclose($smtp);
                return false;
            }

            // Send EHLO again after TLS
            fputs($smtp, "EHLO " . $_SERVER['HTTP_HOST'] . "\r\n");
            $response = fgets($smtp, 515);
        }

        // Authenticate
        fputs($smtp, "AUTH LOGIN\r\n");
        $response = fgets($smtp, 515);
        if (substr($response, 0, 3) != '334') {
            error_log("AUTH LOGIN failed: $response");
            fclose($smtp);
            return false;
        }

        // Send username
        fputs($smtp, base64_encode(SMTP_USERNAME) . "\r\n");
        $response = fgets($smtp, 515);
        if (substr($response, 0, 3) != '334') {
            error_log("Username authentication failed: $response");
            fclose($smtp);
            return false;
        }

        // Send password
        fputs($smtp, base64_encode(SMTP_PASSWORD) . "\r\n");
        $response = fgets($smtp, 515);
        if (substr($response, 0, 3) != '235') {
            error_log("Password authentication failed: $response");
            fclose($smtp);
            return false;
        }

        // Send MAIL FROM
        fputs($smtp, "MAIL FROM: <" . EMAIL_FROM_ADDRESS . ">\r\n");
        $response = fgets($smtp, 515);
        if (substr($response, 0, 3) != '250') {
            error_log("MAIL FROM failed: $response");
            fclose($smtp);
            return false;
        }

        // Send RCPT TO
        fputs($smtp, "RCPT TO: <$to>\r\n");
        $response = fgets($smtp, 515);
        if (substr($response, 0, 3) != '250') {
            error_log("RCPT TO failed: $response");
            fclose($smtp);
            return false;
        }

        // Send DATA command
        fputs($smtp, "DATA\r\n");
        $response = fgets($smtp, 515);
        if (substr($response, 0, 3) != '354') {
            error_log("DATA command failed: $response");
            fclose($smtp);
            return false;
        }

        // Send email headers and body
        $headers = "MIME-Version: 1.0\r\n";
        $headers .= "Content-type: text/html; charset=UTF-8\r\n";
        $headers .= "From: " . EMAIL_FROM_NAME . " <" . EMAIL_FROM_ADDRESS . ">\r\n";
        $headers .= "Reply-To: " . EMAIL_FROM_ADDRESS . "\r\n";
        $headers .= "Subject: $subject\r\n";
        $headers .= "To: $to\r\n";
        $headers .= "\r\n";

        fputs($smtp, $headers . $message . "\r\n.\r\n");
        $response = fgets($smtp, 515);
        if (substr($response, 0, 3) != '250') {
            error_log("Message sending failed: $response");
            fclose($smtp);
            return false;
        }

        // Send QUIT
        fputs($smtp, "QUIT\r\n");
        fclose($smtp);

        return true;

    } catch (Exception $e) {
        error_log("SMTP Error: " . $e->getMessage());
        return false;
    }
}

function sendFallbackEmail($to, $subject, $message) {
    try {
        // Use PHP's built-in mail() function as fallback
        $headers = "MIME-Version: 1.0\r\n";
        $headers .= "Content-type: text/html; charset=UTF-8\r\n";
        $headers .= "From: " . EMAIL_FROM_NAME . " <" . EMAIL_FROM_ADDRESS . ">\r\n";
        $headers .= "Reply-To: " . EMAIL_FROM_ADDRESS . "\r\n";

        return mail($to, $subject, $message, $headers);
    } catch (Exception $e) {
        error_log("Fallback email error: " . $e->getMessage());
        return false;
    }
}

function logEmailAttempt($type, $to, $success, $error = "") {
    global $pdo;
    try {
        $description = "Email: $type to $to";
        if (!$success) $description .= " - FAILED";
        if (strlen($description) > 100) $description = substr($description, 0, 97) . "...";

        $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (?, ?)");
        $stmt->execute(["Email", $description]);
    } catch (Exception $e) {
        error_log("Failed to log email: " . $e->getMessage());
    }
}
?>